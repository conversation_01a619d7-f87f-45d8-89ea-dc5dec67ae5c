# ✅ Deployment Checklist - FPT eInvoice API to IIS

## 📋 PRE-DEPLOYMENT (Máy Local)

### ☐ 1. Chuẩn bị Virtual Environment
```bash
# Chạy trên máy local
.\setup_local_venv.bat
```
- [ ] Virtual environment được tạo thành công
- [ ] Tất cả dependencies từ requirements.txt được cài đặt
- [ ] wfastcgi được cài đặt trong venv
- [ ] Test import các modules thành công

### ☐ 2. Test Application Local
```bash
.\test_local.bat
```
- [ ] WSGI application load thành công
- [ ] .env file được tạo và cấu hình
- [ ] API endpoints hoạt động local
- [ ] Selenium automation test thành công

### ☐ 3. Chuẩn bị Deployment Package
```bash
.\prepare_deployment.bat
```
- [ ] Deployment package được tạo trong thư mục `deployment_package`
- [ ] Virtual environment được copy đầy đủ
- [ ] Tất cả Python files được copy
- [ ] web.config.server được tạo với đường dẫn server
- [ ] Scripts cài đặt được tạo

## 📋 SERVER PREPARATION

### ☐ 4. Kiểm tra Server Requirements
- [ ] Windows Server với IIS đã cài đặt
- [ ] FastCGI module enabled trong IIS
- [ ] Quyền Administrator để cài đặt
- [ ] Port 5959 available (hoặc port khác)
- [ ] Đủ disk space cho deployment package

### ☐ 5. Copy Files to Server
```bash
# Copy deployment_package to server
# Target: C:\inetpub\wwwroot\FPTInvoiceAPI
```
- [ ] Copy toàn bộ deployment_package lên server
- [ ] Verify tất cả files được copy đầy đủ
- [ ] Kiểm tra venv directory structure
- [ ] Kiểm tra permissions cho thư mục

## 📋 IIS CONFIGURATION

### ☐ 6. Chạy Installation Script
```powershell
# Trên server, chạy với quyền Administrator
cd C:\inetpub\wwwroot\FPTInvoiceAPI
.\install_on_server.bat
```
- [ ] .env file được tạo từ template
- [ ] wfastcgi được enable
- [ ] Permissions được set cho IIS_IUSRS

### ☐ 7. Cấu hình IIS Site
```powershell
.\setup_iis.ps1 -AppPath "C:\inetpub\wwwroot\FPTInvoiceAPI"
```
- [ ] IIS features được cài đặt
- [ ] Application Pool được tạo
- [ ] Website được tạo với port 5959
- [ ] FastCGI được cấu hình với venv Python
- [ ] Permissions được set đúng

### ☐ 8. Cấu hình Environment
- [ ] Edit .env file với production values:
  - [ ] API_AUTH_KEY (strong random key)
  - [ ] HEADLESS_MODE=true
  - [ ] FLASK_ENV=production
  - [ ] Log paths
- [ ] Verify web.config paths đúng
- [ ] Tạo thư mục logs với permissions

## 📋 TESTING & VALIDATION

### ☐ 9. Run Deployment Tests
```powershell
.\test_server_deployment.ps1 -ApiKey "your_api_key"
```
- [ ] Required files check passed
- [ ] IIS site running
- [ ] Application Pool started
- [ ] Python environment test passed
- [ ] WSGI application loads
- [ ] HTTP endpoints accessible

### ☐ 10. Functional Testing
```bash
# Test các endpoints chính
curl http://server:5959/health
curl -H "Authorization: Bearer API_KEY" http://server:5959/token-info
```
- [ ] Health endpoint returns 200
- [ ] Root endpoint accessible
- [ ] Authentication working
- [ ] Token info endpoint working
- [ ] PDF generation test (if applicable)

### ☐ 11. Performance & Load Testing
- [ ] Response time < 5 seconds cho health check
- [ ] Memory usage reasonable
- [ ] No memory leaks sau multiple requests
- [ ] Selenium automation working trong headless mode

## 📋 SECURITY & PRODUCTION READINESS

### ☐ 12. Security Configuration
- [ ] Strong API_AUTH_KEY được set
- [ ] Debug mode disabled (FLASK_ENV=production)
- [ ] Sensitive files không accessible qua web
- [ ] Firewall rules configured (nếu cần)
- [ ] SSL certificate installed (nếu cần HTTPS)

### ☐ 13. Monitoring Setup
- [ ] Log files được tạo và writable
- [ ] Log rotation configured (nếu cần)
- [ ] Health check endpoint documented
- [ ] Monitoring alerts setup (nếu có)

### ☐ 14. Backup & Recovery
- [ ] Backup deployment package
- [ ] Document rollback procedure
- [ ] Backup .env và web.config
- [ ] Test restore procedure

## 📋 POST-DEPLOYMENT

### ☐ 15. Documentation
- [ ] Update deployment documentation
- [ ] Document server-specific configurations
- [ ] Create operational runbook
- [ ] Share access credentials securely

### ☐ 16. Team Handover
- [ ] Train operations team
- [ ] Document troubleshooting procedures
- [ ] Share monitoring dashboards
- [ ] Establish support procedures

## 🚨 ROLLBACK PLAN

### ☐ Emergency Rollback Steps
1. [ ] Stop IIS Application Pool
2. [ ] Restore previous deployment package
3. [ ] Restore previous web.config
4. [ ] Start Application Pool
5. [ ] Verify rollback successful

## 📞 CONTACTS & RESOURCES

### Key Files Locations
- **Application**: `C:\inetpub\wwwroot\FPTInvoiceAPI\`
- **Logs**: `C:\inetpub\wwwroot\FPTInvoiceAPI\logs\`
- **IIS Logs**: `C:\inetpub\logs\LogFiles\W3SVC1\`
- **Config**: `C:\inetpub\wwwroot\FPTInvoiceAPI\web.config`

### Key Commands
```powershell
# Health check
Invoke-RestMethod "http://localhost:5959/health"

# Restart app
Restart-WebAppPool -Name "FPTInvoiceAPIPool"

# View logs
Get-Content C:\inetpub\wwwroot\FPTInvoiceAPI\logs\app.log -Tail 20

# Test deployment
.\test_server_deployment.ps1
```

### Support Resources
- **Troubleshooting Guide**: `TROUBLESHOOTING_GUIDE.md`
- **Deployment Guide**: `DEPLOYMENT_GUIDE.md`
- **Application Logs**: Real-time monitoring available

---

## ✅ SIGN-OFF

- [ ] **Developer**: Deployment package tested locally
- [ ] **DevOps**: Server configuration completed
- [ ] **QA**: Functional testing passed
- [ ] **Operations**: Monitoring and alerts configured
- [ ] **Security**: Security review completed
- [ ] **Product Owner**: Acceptance criteria met

**Deployment Date**: _______________
**Deployed By**: _______________
**Approved By**: _______________
