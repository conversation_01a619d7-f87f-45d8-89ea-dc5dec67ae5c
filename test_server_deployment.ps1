# PowerShell Script để test deployment trên server
# Chạy sau khi đã deploy và cấu hình IIS

param(
    [string]$SiteName = "FPTInvoiceAPI",
    [string]$Port = "5959",
    [string]$AppPath = "C:\inetpub\wwwroot\FPTInvoiceAPI",
    [string]$ApiKey = ""
)

Write-Host "🧪 TESTING FPT EINVOICE API DEPLOYMENT" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Green

# 1. Kiểm tra files cần thiết
Write-Host "📁 Checking required files..." -ForegroundColor Cyan

$RequiredFiles = @(
    "wsgi.py",
    "api_server.py", 
    "web.config",
    ".env",
    "venv\Scripts\python.exe",
    "venv\Lib\site-packages\wfastcgi.py"
)

$MissingFiles = @()
foreach ($file in $RequiredFiles) {
    $filePath = Join-Path $AppPath $file
    if (Test-Path $filePath) {
        Write-Host "  ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file" -ForegroundColor Red
        $MissingFiles += $file
    }
}

if ($MissingFiles.Count -gt 0) {
    Write-Host "❌ Missing files detected!" -ForegroundColor Red
    Write-Host "Missing: $($MissingFiles -join ', ')" -ForegroundColor Red
    exit 1
}

# 2. Kiểm tra IIS Site
Write-Host "🌐 Checking IIS site..." -ForegroundColor Cyan

Import-Module WebAdministration -ErrorAction SilentlyContinue

$site = Get-Website -Name $SiteName -ErrorAction SilentlyContinue
if ($site) {
    Write-Host "  ✅ Site exists: $SiteName" -ForegroundColor Green
    Write-Host "  📍 Physical Path: $($site.PhysicalPath)" -ForegroundColor Gray
    Write-Host "  🔌 Port: $($site.Bindings.Collection.bindingInformation)" -ForegroundColor Gray
    
    # Kiểm tra site state
    if ($site.State -eq "Started") {
        Write-Host "  ✅ Site is running" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️ Site state: $($site.State)" -ForegroundColor Yellow
        Write-Host "  Starting site..." -ForegroundColor Yellow
        Start-Website -Name $SiteName
    }
} else {
    Write-Host "  ❌ Site not found: $SiteName" -ForegroundColor Red
    exit 1
}

# 3. Kiểm tra Application Pool
Write-Host "🏊 Checking Application Pool..." -ForegroundColor Cyan

$poolName = "${SiteName}Pool"
$pool = Get-IISAppPool -Name $poolName -ErrorAction SilentlyContinue

if ($pool) {
    Write-Host "  ✅ App Pool exists: $poolName" -ForegroundColor Green
    Write-Host "  📊 State: $($pool.State)" -ForegroundColor Gray
    
    if ($pool.State -eq "Started") {
        Write-Host "  ✅ App Pool is running" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️ Starting App Pool..." -ForegroundColor Yellow
        Start-WebAppPool -Name $poolName
    }
} else {
    Write-Host "  ❌ App Pool not found: $poolName" -ForegroundColor Red
}

# 4. Test Python environment
Write-Host "🐍 Testing Python environment..." -ForegroundColor Cyan

$pythonPath = Join-Path $AppPath "venv\Scripts\python.exe"
if (Test-Path $pythonPath) {
    Write-Host "  ✅ Python executable found" -ForegroundColor Green
    
    # Test Python version
    $pythonVersion = & $pythonPath --version 2>&1
    Write-Host "  📊 Version: $pythonVersion" -ForegroundColor Gray
    
    # Test imports
    Write-Host "  🧪 Testing imports..." -ForegroundColor Gray
    $importTest = & $pythonPath -c "import flask, requests, selenium, dotenv; print('SUCCESS')" 2>&1
    if ($importTest -like "*SUCCESS*") {
        Write-Host "  ✅ All imports successful" -ForegroundColor Green
    } else {
        Write-Host "  ❌ Import test failed: $importTest" -ForegroundColor Red
    }
    
    # Test WSGI
    Write-Host "  🧪 Testing WSGI application..." -ForegroundColor Gray
    Push-Location $AppPath
    $wsgiTest = & $pythonPath -c "from wsgi import application; print('WSGI_SUCCESS')" 2>&1
    Pop-Location
    
    if ($wsgiTest -like "*WSGI_SUCCESS*") {
        Write-Host "  ✅ WSGI application loads successfully" -ForegroundColor Green
    } else {
        Write-Host "  ❌ WSGI test failed: $wsgiTest" -ForegroundColor Red
    }
} else {
    Write-Host "  ❌ Python executable not found: $pythonPath" -ForegroundColor Red
}

# 5. Test HTTP endpoints
Write-Host "🌐 Testing HTTP endpoints..." -ForegroundColor Cyan

$baseUrl = "http://localhost:$Port"

# Test health endpoint
try {
    Write-Host "  🧪 Testing /health..." -ForegroundColor Gray
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/health" -Method GET -TimeoutSec 10
    Write-Host "  ✅ Health check passed" -ForegroundColor Green
    Write-Host "  📊 Status: $($healthResponse.status)" -ForegroundColor Gray
} catch {
    Write-Host "  ❌ Health check failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test root endpoint
try {
    Write-Host "  🧪 Testing / (root)..." -ForegroundColor Gray
    $rootResponse = Invoke-RestMethod -Uri "$baseUrl/" -Method GET -TimeoutSec 10
    Write-Host "  ✅ Root endpoint accessible" -ForegroundColor Green
    Write-Host "  📊 Message: $($rootResponse.message)" -ForegroundColor Gray
} catch {
    Write-Host "  ❌ Root endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test authenticated endpoint (if API key provided)
if (-not [string]::IsNullOrEmpty($ApiKey)) {
    try {
        Write-Host "  🧪 Testing /token-info (authenticated)..." -ForegroundColor Gray
        $headers = @{ "Authorization" = "Bearer $ApiKey" }
        $tokenResponse = Invoke-RestMethod -Uri "$baseUrl/token-info" -Method GET -Headers $headers -TimeoutSec 10
        Write-Host "  ✅ Authentication test passed" -ForegroundColor Green
    } catch {
        Write-Host "  ❌ Authentication test failed: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "  ⚠️ Skipping authentication test (no API key provided)" -ForegroundColor Yellow
    Write-Host "    Use: -ApiKey 'your_key' to test authentication" -ForegroundColor Gray
}

# 6. Kiểm tra logs
Write-Host "📝 Checking logs..." -ForegroundColor Cyan

$logPaths = @(
    (Join-Path $AppPath "logs\app.log"),
    (Join-Path $AppPath "logs\wsgi.log"),
    (Join-Path $AppPath "app.log")
)

foreach ($logPath in $logPaths) {
    if (Test-Path $logPath) {
        Write-Host "  ✅ Log file exists: $logPath" -ForegroundColor Green
        $logSize = (Get-Item $logPath).Length
        Write-Host "    📊 Size: $logSize bytes" -ForegroundColor Gray
        
        # Show last few lines
        $lastLines = Get-Content $logPath -Tail 3 -ErrorAction SilentlyContinue
        if ($lastLines) {
            Write-Host "    📄 Last entries:" -ForegroundColor Gray
            foreach ($line in $lastLines) {
                Write-Host "      $line" -ForegroundColor DarkGray
            }
        }
    }
}

# 7. Tóm tắt kết quả
Write-Host ""
Write-Host "📋 DEPLOYMENT TEST SUMMARY" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Green
Write-Host "🌐 Site URL: $baseUrl" -ForegroundColor White
Write-Host "📁 App Path: $AppPath" -ForegroundColor White
Write-Host "🐍 Python: $pythonPath" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Troubleshooting tips:" -ForegroundColor Yellow
Write-Host "  - Check IIS logs: C:\inetpub\logs\LogFiles\" -ForegroundColor White
Write-Host "  - Check app logs: $AppPath\logs\" -ForegroundColor White
Write-Host "  - Check Event Viewer: Windows Logs > Application" -ForegroundColor White
Write-Host "  - Restart App Pool: Restart-WebAppPool -Name $poolName" -ForegroundColor White
Write-Host ""
