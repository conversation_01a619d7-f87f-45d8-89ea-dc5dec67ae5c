#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho Invoice Data Mapper
"""

import json
import os
from map_invoice import InvoiceMapper, load_and_convert_invoice

def test_basic_conversion():
    """Test chuyển đổi cơ bản"""
    print("🧪 Test 1: Basic Conversion")
    print("-" * 30)
    
    try:
        # Test với file mẫu
        input_file = "sample/input_invoice.json"
        output_file = "sample/test_output.json"
        
        result = load_and_convert_invoice(input_file, output_file)
        
        # Kiểm tra kết quả
        assert result is not None, "Result không được None"
        assert "bname" in result, "Thiếu trường bname"
        assert "items" in result, "Thiếu trường items"
        assert len(result["items"]) > 0, "Phải có ít nhất 1 item"
        
        print("✅ Basic conversion test PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Basic conversion test FAILED: {e}")
        return False

def test_validation():
    """Test validation logic"""
    print("\n🧪 Test 2: Validation")
    print("-" * 30)
    
    mapper = InvoiceMapper()
    
    # Test case 1: Valid data
    try:
        valid_data = {
            "user": {"username": "test", "password": "test"},
            "inv": {
                "bname": "Test Company",
                "type": "01GTKT", 
                "serial": "K23TAB",
                "items": [{"name": "Test item", "amount": 1000}]
            }
        }
        
        result = mapper.validate_input_data(valid_data)
        assert result == True, "Valid data should pass validation"
        print("✅ Valid data test PASSED")
        
    except Exception as e:
        print(f"❌ Valid data test FAILED: {e}")
        return False
    
    # Test case 2: Invalid data (missing required fields)
    try:
        invalid_data = {
            "user": {"username": "test"},  # Missing password
            "inv": {"bname": "Test"}  # Missing required fields
        }
        
        mapper.validate_input_data(invalid_data)
        print("❌ Invalid data test FAILED: Should have raised exception")
        return False
        
    except ValueError as e:
        print("✅ Invalid data test PASSED: Correctly caught validation error")
        
    except Exception as e:
        print(f"❌ Invalid data test FAILED: Wrong exception type: {e}")
        return False
    
    return True

def test_safe_float():
    """Test safe float conversion"""
    print("\n🧪 Test 3: Safe Float Conversion")
    print("-" * 30)
    
    mapper = InvoiceMapper()
    
    test_cases = [
        ("123.45", 123.45),
        ("", 0.0),
        (None, 0.0),
        ("invalid", 0.0),
        (123, 123.0),
        ("0", 0.0)
    ]
    
    for input_val, expected in test_cases:
        result = mapper.safe_float(input_val)
        if result == expected:
            print(f"✅ safe_float({input_val}) = {result}")
        else:
            print(f"❌ safe_float({input_val}) = {result}, expected {expected}")
            return False
    
    print("✅ Safe float conversion test PASSED")
    return True

def test_totals_calculation():
    """Test tính toán totals"""
    print("\n🧪 Test 4: Totals Calculation")
    print("-" * 30)
    
    mapper = InvoiceMapper()
    
    # Test items
    items = [
        {"amount": 1000, "vrt": "10"},  # VAT = 100
        {"amount": 2000, "vrt": "8"},   # VAT = 160
        {"amount": 500, "vrt": "0"}     # VAT = 0
    ]
    
    result = mapper.calculate_totals(items)
    
    expected_sum = 3500  # 1000 + 2000 + 500
    expected_vat = 260   # 100 + 160 + 0
    expected_total = 3760  # 3500 + 260
    
    if (result["sum"] == expected_sum and 
        result["vat"] == expected_vat and 
        result["total"] == expected_total):
        print("✅ Totals calculation test PASSED")
        print(f"   Sum: {result['sum']}, VAT: {result['vat']}, Total: {result['total']}")
        return True
    else:
        print("❌ Totals calculation test FAILED")
        print(f"   Expected: Sum={expected_sum}, VAT={expected_vat}, Total={expected_total}")
        print(f"   Got: Sum={result['sum']}, VAT={result['vat']}, Total={result['total']}")
        return False

def test_items_mapping():
    """Test mapping items"""
    print("\n🧪 Test 5: Items Mapping")
    print("-" * 30)
    
    mapper = InvoiceMapper()
    
    input_items = [
        {
            "line": 1,
            "name": "Test Item 1",
            "unit": "pcs",
            "price": 100,
            "quantity": 10,
            "amount": 1000,
            "vrt": "10"
        }
    ]
    
    result = mapper.map_items(input_items)
    
    if (len(result) == 1 and
        result[0]["name"] == "Test Item 1" and
        result[0]["quantity"] == 10 and
        result[0]["price"] == 100 and
        result[0]["vat"] == 100 and  # 1000 * 10 / 100
        result[0]["vrn"] == "10%"):
        print("✅ Items mapping test PASSED")
        return True
    else:
        print("❌ Items mapping test FAILED")
        print(f"Result: {result}")
        return False

def run_all_tests():
    """Chạy tất cả tests"""
    print("🚀 FPT eInvoice Data Mapper - Test Suite")
    print("=" * 50)
    
    tests = [
        test_basic_conversion,
        test_validation,
        test_safe_float,
        test_totals_calculation,
        test_items_mapping
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} CRASHED: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests PASSED!")
        return 0
    else:
        print("💥 Some tests FAILED!")
        return 1

if __name__ == "__main__":
    exit(run_all_tests())
