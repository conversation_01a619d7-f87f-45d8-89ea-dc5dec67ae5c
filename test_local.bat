@echo off
REM Script test application trên máy local trước khi deploy

echo ========================================
echo  TESTING LOCAL APPLICATION
echo ========================================

REM Kích hoạt venv
if not exist "venv\Scripts\activate.bat" (
    echo ❌ Virtual environment not found!
    echo Please run setup_local_venv.bat first
    pause
    exit /b 1
)

call venv\Scripts\activate.bat

REM Kiểm tra .env file
if not exist ".env" (
    echo ⚠️ .env file not found!
    echo Please create .env file with your configuration
    echo Example content:
    echo   API_AUTH_KEY=your_api_key_here
    echo   HEADLESS_MODE=false
    echo   FLASK_ENV=development
    pause
    exit /b 1
)

REM Test import modules
echo 🧪 Testing Python imports...
python -c "import flask, requests, selenium, dotenv; print('✅ All imports successful')"
if %errorlevel% neq 0 (
    echo ❌ Import test failed
    pause
    exit /b 1
)

REM Test WSGI application
echo 🧪 Testing WSGI application...
python -c "from wsgi import application; print('✅ WSGI application loaded successfully')"
if %errorlevel% neq 0 (
    echo ❌ WSGI test failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo  LOCAL TESTS COMPLETED
echo ========================================
echo.
echo To start local server for testing:
echo   python wsgi.py
echo.
echo To test endpoints:
echo   curl http://localhost:5959/health
echo.
pause
