# 🚀 FPT eInvoice API - IIS Deployment Guide

## 📋 Quy trình triển khai nhanh

### 1. Chu<PERSON>n bị trên máy local

```bash
# Tạo virtual environment và cài dependencies
.\setup_local_venv.bat

# Test application
.\test_local.bat

# Tạo deployment package
.\prepare_deployment.bat
```

### 2. Deploy lên server

```bash
# Copy thư mục deployment_package lên server
# Ví dụ: C:\inetpub\wwwroot\FPTInvoiceAPI

# Chạy script cài đặt trên server
.\install_on_server.bat

# Chỉnh sửa .env với thông tin thực tế
notepad .env
```

### 3. Cấu hình IIS

1. **Tạo Application Pool:**
   - Name: `FPTInvoiceAPIPool`
   - .NET CLR version: `No Managed Code`
   - Managed pipeline mode: `Integrated`

2. **Tạo Website:**
   - Site name: `FPTInvoiceAPI`
   - Physical path: `C:\inetpub\wwwroot\FPTInvoiceAPI`
   - Port: `5959`
   - Application pool: `FPTInvoiceAPIPool`

3. **Cấu hình FastCGI:**
   - Cập nhật `web.config` với đường dẫn Python đúng
   - Đảm bảo wfastcgi đã được enable

### 4. Test deployment

```bash
# Health check
curl http://localhost:5959/health

# Test với authentication
curl -H "Authorization: Bearer YOUR_API_KEY" http://localhost:5959/token-info
```

## 🔧 File cấu hình quan trọng

### `.env` (cần chỉnh sửa)
```env
API_AUTH_KEY=your_production_api_key_here
HEADLESS_MODE=true
FLASK_ENV=production
```

### `web.config` (cập nhật đường dẫn)
```xml
<add name="PythonHandler" 
     scriptProcessor="C:\inetpub\wwwroot\FPTInvoiceAPI\venv\Scripts\python.exe|C:\inetpub\wwwroot\FPTInvoiceAPI\venv\Lib\site-packages\wfastcgi.py" />
```

## 🐛 Troubleshooting

### Lỗi 500 Internal Server Error
```bash
# Kiểm tra Python path
C:\inetpub\wwwroot\FPTInvoiceAPI\venv\Scripts\python.exe --version

# Test WSGI
cd C:\inetpub\wwwroot\FPTInvoiceAPI
venv\Scripts\python.exe -c "from wsgi import application; print('OK')"

# Restart Application Pool
Restart-WebAppPool -Name "FPTInvoiceAPIPool"
```

### Lỗi Module Not Found
```bash
# Reinstall dependencies
cd C:\inetpub\wwwroot\FPTInvoiceAPI
venv\Scripts\python.exe -m pip install -r requirements.txt
```

### Lỗi Permissions
```bash
# Set permissions
icacls C:\inetpub\wwwroot\FPTInvoiceAPI /grant "IIS_IUSRS:(OI)(CI)F" /T
```

## 📊 Monitoring

- **Application Logs**: `logs/app.log`
- **IIS Logs**: `C:\inetpub\logs\LogFiles\W3SVC1\`
- **Health Check**: `GET /health`

## 🔄 Updates

```bash
# Stop Application Pool
Stop-WebAppPool -Name "FPTInvoiceAPIPool"

# Update code
# Copy new files...

# Start Application Pool
Start-WebAppPool -Name "FPTInvoiceAPIPool"
```
