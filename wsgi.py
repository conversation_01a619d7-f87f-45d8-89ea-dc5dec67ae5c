#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WSGI Entry Point cho FPT eInvoice API Server
Dù<PERSON> để deploy với IIS hoặc các WSGI servers khác
"""

import os
import sys
import logging
from pathlib import Path

# Thêm thư mục hiện tại vào Python path
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# Setup logging cho production
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

try:
    # Import Flask app
    from api_server import app
    
    # Đảm bảo app chạy trong production mode
    app.config['ENV'] = 'production'
    app.config['DEBUG'] = False
    
    logger.info("✅ WSGI application loaded successfully")
    logger.info(f"📁 Working directory: {current_dir}")
    logger.info(f"🐍 Python path: {sys.path[:3]}")
    
    # WSGI application object
    application = app
    
except Exception as e:
    logger.error(f"❌ Failed to load WSGI application: {e}")
    raise

if __name__ == "__main__":
    # Chạy với Waitress WSGI server cho testing
    from waitress import serve
    
    logger.info("🚀 Starting with Waitress WSGI server")
    logger.info("📍 Server: http://localhost:5959")
    
    serve(
        application,
        host='0.0.0.0',
        port=5959,
        threads=4,
        connection_limit=100,
        cleanup_interval=30,
        channel_timeout=120
    )
