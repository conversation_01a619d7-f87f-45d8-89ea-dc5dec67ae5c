# Hướng dẫn Deploy FPT eInvoice API với IIS

## 📋 <PERSON><PERSON><PERSON> c<PERSON>u hệ thống

- Windows Server 2016+ hoặc Windows 10+
- Python 3.8+ (k<PERSON><PERSON><PERSON>n nghị Python 3.12)
- IIS 8.0+
- <PERSON><PERSON><PERSON>n Administrator

## 🚀 Cách 1: Deploy với IIS (Khuyến nghị cho Production)

### Bước 1: Chu<PERSON><PERSON> bị môi trường

```powershell
# 1. Cài đặt Python dependencies
pip install -r requirements.txt

# 2. Tạo file .env từ template
copy .env.template .env
# Chỉnh sửa .env với các giá trị thực tế

# 3. <PERSON><PERSON><PERSON> thư mục logs
mkdir logs
```

### Bước 2: Cài đặt IIS

```powershell
# Chạy PowerShell với quyền Administrator
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Chạy script cài đặt IIS
.\setup_iis.ps1

# Hoặc với custom parameters
.\setup_iis.ps1 -SiteName "MyFPTAPI" -Port "8080" -AppPath "C:\MyApp\FPT_INVOICE_PREVIEW"
```

### Bước 3: <PERSON><PERSON><PERSON> hình web.config

Chỉnh sửa file `web.config` và cập nhật các đường dẫn:

```xml
<!-- Cập nhật đường dẫn Python -->
<add name="PythonHandler" 
     scriptProcessor="C:\Python312\python.exe|C:\Python312\Lib\site-packages\wfastcgi.py" />

<!-- Cập nhật đường dẫn project -->
<add name="PYTHONPATH" value="C:\LjKenji\FPT_INVOICE_PREVIEW" />
```

### Bước 4: Test deployment

```powershell
# Test website
curl http://localhost:5959/

# Test health endpoint
curl http://localhost:5959/health

# Test với authentication
curl -H "Authorization: Bearer your_api_key" http://localhost:5959/token-info
```

## 🔧 Cách 2: Deploy với Waitress (Alternative)

### Chạy trực tiếp với Waitress

```bash
# Development
python wsgi.py

# Production
python start_production.py
```

### Chạy như Windows Service

```powershell
# Cài đặt NSSM (Non-Sucking Service Manager)
# Download từ: https://nssm.cc/download

# Tạo service
nssm install FPTInvoiceAPI "C:\Python312\python.exe" "C:\LjKenji\FPT_INVOICE_PREVIEW\start_production.py"
nssm set FPTInvoiceAPI AppDirectory "C:\LjKenji\FPT_INVOICE_PREVIEW"
nssm set FPTInvoiceAPI DisplayName "FPT eInvoice API Service"
nssm set FPTInvoiceAPI Description "FPT eInvoice API Server"

# Start service
nssm start FPTInvoiceAPI
```

## 📊 Monitoring và Logs

### Log Files

- **Application Logs**: `logs/app.log`
- **WSGI Logs**: `logs/wsgi.log`
- **IIS Logs**: `C:\inetpub\logs\LogFiles\W3SVC1\`

### Health Check Endpoints

```bash
# Basic health check
GET /health

# Token information (requires auth)
GET /token-info
```

## 🔒 Security Configuration

### 1. Firewall Rules

```powershell
# Mở port cho API
New-NetFirewallRule -DisplayName "FPT Invoice API" -Direction Inbound -Protocol TCP -LocalPort 5959 -Action Allow
```

### 2. SSL/HTTPS (Khuyến nghị)

```powershell
# Bind SSL certificate
netsh http add sslcert ipport=0.0.0.0:443 certhash=YOUR_CERT_HASH appid={YOUR_APP_ID}
```

### 3. Environment Variables

Đảm bảo các biến môi trường được cấu hình đúng trong `.env`:

```env
API_AUTH_KEY=strong_random_key_here
HEADLESS_MODE=true
FLASK_ENV=production
```

## 🐛 Troubleshooting

### Lỗi thường gặp

1. **500 Internal Server Error**
   ```bash
   # Kiểm tra logs
   tail -f logs/app.log
   tail -f logs/wsgi.log
   ```

2. **Python module not found**
   ```bash
   # Kiểm tra PYTHONPATH trong web.config
   # Đảm bảo virtual environment được activate
   ```

3. **Permission denied**
   ```powershell
   # Cấp quyền cho IIS_IUSRS
   icacls C:\LjKenji\FPT_INVOICE_PREVIEW /grant "IIS_IUSRS:(OI)(CI)F" /T
   ```

4. **FastCGI timeout**
   ```xml
   <!-- Tăng timeout trong web.config -->
   <fastCgi>
     <application requestTimeout="300" activityTimeout="300" />
   </fastCgi>
   ```

### Performance Tuning

1. **Application Pool Settings**
   ```powershell
   # Tăng memory limit
   Set-ItemProperty "IIS:\AppPools\FPTInvoiceAPIPool" -Name "processModel.memoryLimit" -Value 0
   
   # Tăng số worker processes
   Set-ItemProperty "IIS:\AppPools\FPTInvoiceAPIPool" -Name "processModel.maxProcesses" -Value 4
   ```

2. **FastCGI Settings**
   ```xml
   <fastCgi>
     <application maxInstances="8" instanceMaxRequests="1000" />
   </fastCgi>
   ```

## 📞 Support

Nếu gặp vấn đề, kiểm tra:

1. **Logs**: `logs/app.log`, `logs/wsgi.log`
2. **IIS Manager**: Kiểm tra Application Pool status
3. **Event Viewer**: Windows Logs > Application
4. **Process Monitor**: Theo dõi file access và permissions

## 🔄 Updates và Maintenance

### Deploy updates

```powershell
# 1. Stop application pool
Stop-WebAppPool -Name "FPTInvoiceAPIPool"

# 2. Update code
git pull origin main
pip install -r requirements.txt

# 3. Start application pool
Start-WebAppPool -Name "FPTInvoiceAPIPool"
```

### Backup

```powershell
# Backup configuration
xcopy C:\LjKenji\FPT_INVOICE_PREVIEW\*.config C:\Backup\
xcopy C:\LjKenji\FPT_INVOICE_PREVIEW\*.env C:\Backup\
```
