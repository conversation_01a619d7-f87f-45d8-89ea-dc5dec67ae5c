# 🔧 Troubleshooting Guide - FPT eInvoice API on IIS

## 🚨 Common Issues và Solutions

### 1. **500 Internal Server Error**

#### Nguyên nhân thường gặp:
- Python path không đúng trong web.config
- WSGI application không load được
- Thiếu permissions
- Environment variables không đúng

#### Cách khắc phục:

```powershell
# 1. Kiểm tra Python path
C:\inetpub\wwwroot\FPTInvoiceAPI\venv\Scripts\python.exe --version

# 2. Test WSGI application
cd C:\inetpub\wwwroot\FPTInvoiceAPI
venv\Scripts\python.exe -c "from wsgi import application; print('OK')"

# 3. Kiểm tra permissions
icacls C:\inetpub\wwwroot\FPTInvoiceAPI /grant "IIS_IUSRS:(OI)(CI)F" /T

# 4. Restart Application Pool
Restart-WebAppPool -Name "FPTInvoiceAPIPool"
```

### 2. **Module Not Found Error**

#### Nguyên nhân:
- Virtual environment không được copy đầy đủ
- PYTHONPATH không đúng
- Dependencies thiếu

#### Cách khắc phục:

```powershell
# 1. Kiểm tra venv structure
dir C:\inetpub\wwwroot\FPTInvoiceAPI\venv\Lib\site-packages\

# 2. Reinstall dependencies trong venv
cd C:\inetpub\wwwroot\FPTInvoiceAPI
venv\Scripts\python.exe -m pip install -r requirements.txt

# 3. Test imports
venv\Scripts\python.exe -c "import flask, requests, selenium, dotenv"
```

### 3. **FastCGI Timeout**

#### Nguyên nhân:
- Request xử lý quá lâu (Selenium automation)
- Timeout settings quá thấp

#### Cách khắc phục:

Cập nhật `web.config`:
```xml
<fastCgi>
  <application requestTimeout="300" activityTimeout="300" idleTimeout="1800">
    <!-- Tăng timeout lên 5 phút -->
  </application>
</fastCgi>
```

### 4. **Authentication Failed**

#### Nguyên nhân:
- API_AUTH_KEY không được set
- .env file không được load

#### Cách khắc phục:

```powershell
# 1. Kiểm tra .env file
type C:\inetpub\wwwroot\FPTInvoiceAPI\.env

# 2. Test environment loading
cd C:\inetpub\wwwroot\FPTInvoiceAPI
venv\Scripts\python.exe -c "from dotenv import load_dotenv; import os; load_dotenv(); print(os.getenv('API_AUTH_KEY'))"
```

### 5. **Selenium WebDriver Issues**

#### Nguyên nhân:
- Chrome/ChromeDriver không tương thích
- Headless mode issues trên server
- Permissions cho browser

#### Cách khắc phục:

```powershell
# 1. Set headless mode trong .env
echo HEADLESS_MODE=true >> .env

# 2. Test selenium
cd C:\inetpub\wwwroot\FPTInvoiceAPI
venv\Scripts\python.exe selenium_automation.py

# 3. Cài đặt Chrome stable version
# Download từ: https://www.google.com/chrome/
```

## 📊 Monitoring và Logs

### 1. **Application Logs**

```powershell
# Real-time monitoring
Get-Content C:\inetpub\wwwroot\FPTInvoiceAPI\logs\app.log -Wait -Tail 10

# Search for errors
Select-String -Path "C:\inetpub\wwwroot\FPTInvoiceAPI\logs\*.log" -Pattern "ERROR|CRITICAL"
```

### 2. **IIS Logs**

```powershell
# IIS access logs
Get-Content C:\inetpub\logs\LogFiles\W3SVC1\*.log -Tail 20

# Failed request tracing (nếu enabled)
dir C:\inetpub\logs\FailedReqLogFiles\
```

### 3. **Windows Event Logs**

```powershell
# Application events
Get-EventLog -LogName Application -Source "Python*" -Newest 10

# System events
Get-EventLog -LogName System -EntryType Error -Newest 5
```

## 🔧 Performance Tuning

### 1. **Application Pool Settings**

```powershell
# Tăng memory limit
Set-ItemProperty "IIS:\AppPools\FPTInvoiceAPIPool" -Name "processModel.memoryLimit" -Value 0

# Tăng số worker processes
Set-ItemProperty "IIS:\AppPools\FPTInvoiceAPIPool" -Name "processModel.maxProcesses" -Value 2

# Disable idle timeout
Set-ItemProperty "IIS:\AppPools\FPTInvoiceAPIPool" -Name "processModel.idleTimeout" -Value "00:00:00"
```

### 2. **FastCGI Optimization**

Cập nhật `web.config`:
```xml
<fastCgi>
  <application maxInstances="4" 
               instanceMaxRequests="1000"
               requestTimeout="300"
               activityTimeout="300">
  </application>
</fastCgi>
```

### 3. **Request Filtering**

```xml
<system.webServer>
  <security>
    <requestFiltering>
      <requestLimits maxAllowedContentLength="104857600" /> <!-- 100MB -->
    </requestFiltering>
  </security>
</system.webServer>
```

## 🛠️ Diagnostic Commands

### Quick Health Check Script

```powershell
# Save as check_health.ps1
param([string]$Port = "5959")

Write-Host "🏥 FPT eInvoice API Health Check" -ForegroundColor Green

# 1. Test HTTP
try {
    $response = Invoke-RestMethod "http://localhost:$Port/health" -TimeoutSec 5
    Write-Host "✅ HTTP: $($response.status)" -ForegroundColor Green
} catch {
    Write-Host "❌ HTTP: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. Check processes
$pythonProcesses = Get-Process -Name "python" -ErrorAction SilentlyContinue
Write-Host "🐍 Python processes: $($pythonProcesses.Count)" -ForegroundColor Yellow

# 3. Check app pool
$pool = Get-IISAppPool -Name "FPTInvoiceAPIPool" -ErrorAction SilentlyContinue
if ($pool) {
    Write-Host "🏊 App Pool: $($pool.State)" -ForegroundColor Yellow
} else {
    Write-Host "❌ App Pool not found" -ForegroundColor Red
}

# 4. Check recent logs
$logFile = "C:\inetpub\wwwroot\FPTInvoiceAPI\logs\app.log"
if (Test-Path $logFile) {
    $lastLog = Get-Content $logFile -Tail 1
    Write-Host "📝 Last log: $lastLog" -ForegroundColor Gray
}
```

## 🔄 Restart Procedures

### Complete Restart

```powershell
# 1. Stop Application Pool
Stop-WebAppPool -Name "FPTInvoiceAPIPool"

# 2. Kill any hanging Python processes
Get-Process -Name "python" | Where-Object {$_.Path -like "*FPTInvoiceAPI*"} | Stop-Process -Force

# 3. Clear temp files
Remove-Item "C:\inetpub\wwwroot\FPTInvoiceAPI\__pycache__" -Recurse -Force -ErrorAction SilentlyContinue

# 4. Start Application Pool
Start-WebAppPool -Name "FPTInvoiceAPIPool"

# 5. Test
Start-Sleep 5
Invoke-RestMethod "http://localhost:5959/health"
```

### Graceful Restart

```powershell
# Recycle Application Pool (recommended)
Restart-WebAppPool -Name "FPTInvoiceAPIPool"
```

## 📞 Emergency Contacts

### Log Locations Summary
- **App Logs**: `C:\inetpub\wwwroot\FPTInvoiceAPI\logs\`
- **IIS Logs**: `C:\inetpub\logs\LogFiles\W3SVC1\`
- **Event Logs**: Event Viewer > Windows Logs > Application

### Key Commands
```powershell
# Test deployment
.\test_server_deployment.ps1 -ApiKey "your_key"

# Health check
.\check_health.ps1

# View logs
Get-Content C:\inetpub\wwwroot\FPTInvoiceAPI\logs\app.log -Tail 20

# Restart
Restart-WebAppPool -Name "FPTInvoiceAPIPool"
```
