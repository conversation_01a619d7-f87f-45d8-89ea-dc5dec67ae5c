# 🔄 FPT eInvoice Data Mapper

Utility để chuyển đổi dữ liệu hóa đơn từ format tùy chỉnh sang format FPT eInvoice API.

## 📋 Tính năng

- ✅ Đọc và parse file JSON đầu vào
- ✅ Chuyển đổi dữ liệu sang format FPT eInvoice API
- ✅ Validation dữ liệu đầu vào
- ✅ Tính toán tự động các tổng tiền và VAT
- ✅ Xử lý an toàn các giá trị số
- ✅ Test integration với API server
- ✅ Export kết quả ra file JSON

## 🚀 Cách sử dụng

### 1. Chuyển đổi cơ bản

```bash
# Chuyển đổi với file mặc định
python map_invoice.py

# Chỉ định file input và output
python map_invoice.py --input my_invoice.json --output converted.json
```

### 2. Test với API server

```bash
# Test với API server local
python map_invoice.py --test-api --api-key "your_api_key"

# Test với API server khác
python map_invoice.py --test-api --api-url "http://server:5959" --api-key "your_key"
```

### 3. Sử dụng trong code

```python
from map_invoice import InvoiceMapper, load_and_convert_invoice

# Cách 1: Sử dụng function tiện ích
converted_data = load_and_convert_invoice("input.json", "output.json")

# Cách 2: Sử dụng class
mapper = InvoiceMapper()
input_data = mapper.load_input_invoice("input.json")
mapper.validate_input_data(input_data)
converted_data = mapper.map_invoice_data(input_data)
```

## 📊 Format dữ liệu

### Input Format (input_invoice.json)

```json
{
  "user": {
    "username": "0106512255.ALS6",
    "password": "b254aead"
  },
  "inv": {
    "sid": "15278832",
    "type": "01GTKT",
    "serial": "K23TAB",
    "bname": "VUONG VAN DUONG",
    "buyer": "VUONG VAN DUONG",
    "btax": "",
    "baddr": "SON DU NGUYEN KHE DONG ANH HN",
    "paym": "TM",
    "curr": "VND",
    "sumv": 1081669.0,
    "vatv": 86534.0,
    "totalv": 1168203.0,
    "items": [
      {
        "line": 1,
        "name": "DV vận chuyển mặt đất",
        "unit": "KG",
        "price": 1050.0,
        "quantity": 289.0,
        "amount": 303450.0,
        "vrt": "8"
      }
    ]
  }
}
```

### Output Format (FPT eInvoice API)

```json
{
  "class": "0",
  "bname": "VUONG VAN DUONG",
  "type": "01GTKT",
  "serial": "K23TAB",
  "sumv": 1081669.0,
  "vatv": 86534.0,
  "totalv": 1168203.0,
  "items": [
    {
      "quantity": 289.0,
      "price": 1050.0,
      "unit": "KG",
      "vat": 24276.0,
      "amount": 303450.0,
      "total": 327726.0,
      "vrt": "8",
      "vrn": "8%",
      "name": "DV vận chuyển mặt đất"
    }
  ],
  "tax": [
    {
      "vrt": "8",
      "vrn": "8%",
      "amt": 1081669.0,
      "vat": 86533.52
    }
  ]
}
```

## 🔧 Mapping Rules

| Input Field | Output Field | Description |
|-------------|--------------|-------------|
| `inv.bname` | `bname` | Tên khách hàng |
| `inv.type` | `type` | Loại hóa đơn |
| `inv.serial` | `serial` | Ký hiệu hóa đơn |
| `inv.sumv` | `sumv`, `sum` | Tổng tiền chưa VAT |
| `inv.vatv` | `vatv`, `vat` | Tổng VAT |
| `inv.totalv` | `totalv`, `total` | Tổng tiền có VAT |
| `inv.items[]` | `items[]` | Danh sách hàng hóa |

## ⚙️ Tính năng nâng cao

### 1. Auto-calculation

- Tự động tính VAT cho từng item
- Tự động tính tổng tiền theo thuế suất
- Tạo tax summary theo từng mức thuế

### 2. Data validation

- Kiểm tra các trường bắt buộc
- Validate format dữ liệu
- Xử lý an toàn giá trị null/empty

### 3. Error handling

- Safe float conversion
- Graceful error messages
- Detailed logging

## 🧪 Testing

```bash
# Test conversion
python map_invoice.py

# Test với sample data
python map_invoice.py --input sample/input_invoice.json

# Test API integration
python map_invoice.py --test-api --api-key "your_key"
```

## 📝 Logs

Script sẽ tạo logs chi tiết về quá trình chuyển đổi:

```
2025-06-24 13:37:28,842 - INFO - 🔄 Đang load file: sample/input_invoice.json
2025-06-24 13:37:28,843 - INFO - ✅ Validation thành công
2025-06-24 13:37:28,844 - INFO - ✅ Đã lưu kết quả vào: sample/converted_invoice.json
```

## 🔍 Troubleshooting

### Lỗi thường gặp

1. **File not found**
   ```
   FileNotFoundError: File không tồn tại: input.json
   ```
   → Kiểm tra đường dẫn file input

2. **JSON parse error**
   ```
   JSONDecodeError: Expecting ',' delimiter
   ```
   → Kiểm tra format JSON của file input

3. **Missing required fields**
   ```
   ValueError: Thiếu các trường bắt buộc: ['inv.bname']
   ```
   → Đảm bảo file input có đầy đủ các trường bắt buộc

4. **API test failed**
   ```
   API test thất bại: 401 - Authentication required
   ```
   → Kiểm tra API key và server URL

## 📞 Support

- File mẫu: `sample/input_invoice.json`
- Output mẫu: `sample/converted_invoice.json`
- Logs: Console output với timestamp
- API test: Tích hợp với FPT eInvoice API server
