<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>

    <!-- FastCGI Handler cho Python -->
    <handlers>
      <add name="PythonHandler"
           path="*"
           verb="*"
           modules="FastCgiModule"
           scriptProcessor="C:\inetpub\wwwroot\FPTInvoiceAPI\venv\Scripts\python.exe|C:\inetpub\wwwroot\FPTInvoiceAPI\venv\Lib\site-packages\wfastcgi.py"
           resourceType="Unspecified"
           requireAccess="Script" />
    </handlers>

    <!-- FastCGI Settings -->
    <fastCgi>
      <application fullPath="C:\inetpub\wwwroot\FPTInvoiceAPI\venv\Scripts\python.exe"
                   arguments="C:\inetpub\wwwroot\FPTInvoiceAPI\venv\Lib\site-packages\wfastcgi.py"
                   maxInstances="4"
                   idleTimeout="1800"
                   activityTimeout="30"
                   requestTimeout="90"
                   instanceMaxRequests="10000">
        <environmentVariables>
          <!-- WSGI Module và Application -->
          <add name="WSGI_HANDLER" value="wsgi.application" />
          <add name="PYTHONPATH" value="C:\inetpub\wwwroot\FPTInvoiceAPI" />

          <!-- Python Settings -->
          <add name="WSGI_LOG" value="C:\inetpub\wwwroot\FPTInvoiceAPI\logs\wsgi.log" />

          <!-- Application Settings -->
          <add name="FLASK_ENV" value="production" />
          <add name="PYTHONIOENCODING" value="utf-8" />

        </environmentVariables>
      </application>
    </fastCgi>
    
    <!-- Security Settings -->
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="52428800" /> <!-- 50MB -->
      </requestFiltering>
    </security>

    <!-- Static Files -->
    <staticContent>
      <remove fileExtension=".json" />
      <mimeMap fileExtension=".json" mimeType="application/json" />
      <remove fileExtension=".pdf" />
      <mimeMap fileExtension=".pdf" mimeType="application/pdf" />
    </staticContent>

    <!-- Error Pages -->
    <httpErrors errorMode="Detailed" />

  </system.webServer>

  <!-- Application Settings -->
  <appSettings>
    <add key="WSGI_ALT_VIRTUALENV_HANDLER" value="wsgi.application" />
    <add key="WSGI_ALT_VIRTUALENV_ACTIVATE_THIS" value="C:\inetpub\wwwroot\FPTInvoiceAPI\venv\Scripts\activate_this.py" />
  </appSettings>

</configuration>
