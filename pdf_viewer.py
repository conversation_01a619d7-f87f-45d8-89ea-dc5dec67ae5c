#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF Viewer for FPT eInvoice Response
Hiển thị PDF từ response.json của FPT eInvoice API
"""

import json
import base64
import os
import sys
from datetime import datetime

def load_response_data(file_path="sample/response.json"):
    """Load response data từ file JSON"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ Loaded response data from: {file_path}")
        return data
    except FileNotFoundError:
        print(f"❌ File not found: {file_path}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON format: {e}")
        return None

def extract_pdf_from_response(response_data):
    """Trích xuất PDF data từ response"""
    
    # Tìm PDF data trong các trường có thể
    pdf_data = None
    pdf_field = None
    
    # Các trư<PERSON>ng có thể chứa PDF data
    possible_fields = ['pdf', 'data', 'content', 'file', 'base64', 'pdfData']
    
    for field in possible_fields:
        if field in response_data:
            pdf_data = response_data[field]
            pdf_field = field
            print(f"✅ Found PDF data in field: {field}")
            break
    
    if not pdf_data:
        print("❌ No PDF data found in response")
        print(f"Available fields: {list(response_data.keys())}")
        return None
    
    # Kiểm tra và decode base64 nếu cần
    if isinstance(pdf_data, str):
        # Remove data URL prefix if present
        if pdf_data.startswith('data:application/pdf;base64,'):
            pdf_data = pdf_data[len('data:application/pdf;base64,'):]
            print("✅ Removed data URL prefix")
        
        try:
            # Decode base64
            pdf_bytes = base64.b64decode(pdf_data)
            print(f"✅ Decoded base64 PDF: {len(pdf_bytes)} bytes")
            return pdf_bytes
        except Exception as e:
            print(f"❌ Failed to decode base64: {e}")
            return None
    
    elif isinstance(pdf_data, bytes):
        print(f"✅ PDF data is already bytes: {len(pdf_data)} bytes")
        return pdf_data
    
    else:
        print(f"❌ Unexpected PDF data type: {type(pdf_data)}")
        return None

def save_pdf(pdf_bytes, output_dir="./invoices"):
    """Lưu PDF bytes vào file"""
    
    # Tạo thư mục nếu chưa có
    os.makedirs(output_dir, exist_ok=True)
    
    # Tạo tên file với timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"response_pdf_{timestamp}.pdf"
    filepath = os.path.join(output_dir, filename)
    
    try:
        with open(filepath, 'wb') as f:
            f.write(pdf_bytes)
        
        print(f"✅ PDF saved to: {filepath}")
        print(f"📄 File size: {len(pdf_bytes)} bytes")
        return filepath
    
    except Exception as e:
        print(f"❌ Failed to save PDF: {e}")
        return None

def validate_pdf(pdf_bytes):
    """Validate PDF format"""
    
    if not pdf_bytes:
        return False
    
    # Check PDF header
    if pdf_bytes.startswith(b'%PDF'):
        print("✅ Valid PDF format detected")
        return True
    else:
        print("❌ Invalid PDF format - missing PDF header")
        print(f"First 20 bytes: {pdf_bytes[:20]}")
        return False

def open_pdf(filepath):
    """Mở PDF file với default application"""
    
    try:
        if os.name == 'nt':  # Windows
            os.startfile(filepath)
        elif os.name == 'posix':  # macOS and Linux
            os.system(f'open "{filepath}"')  # macOS
            # os.system(f'xdg-open "{filepath}"')  # Linux
        
        print(f"✅ Opened PDF: {filepath}")
        return True
    
    except Exception as e:
        print(f"❌ Failed to open PDF: {e}")
        return False

def main():
    """Main function"""
    
    print("🚀 FPT eInvoice PDF Viewer")
    print("=" * 50)
    
    # Kiểm tra command line arguments
    if len(sys.argv) > 1:
        response_file = sys.argv[1]
    else:
        response_file = "sample/response.json"
    
    print(f"📂 Loading response from: {response_file}")
    
    # Load response data
    response_data = load_response_data(response_file)
    if not response_data:
        return
    
    # Extract PDF
    pdf_bytes = extract_pdf_from_response(response_data)
    if not pdf_bytes:
        return
    
    # Validate PDF
    if not validate_pdf(pdf_bytes):
        return
    
    # Save PDF
    filepath = save_pdf(pdf_bytes)
    if not filepath:
        return
    
    # Open PDF
    print(f"\n💡 Opening PDF viewer...")
    open_pdf(filepath)
    
    print(f"\n✅ Process completed successfully!")
    print(f"📄 PDF file: {filepath}")
    print(f"📊 Size: {len(pdf_bytes)} bytes")

if __name__ == "__main__":
    main()
