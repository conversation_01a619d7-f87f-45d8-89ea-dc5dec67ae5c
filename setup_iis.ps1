# PowerShell Script để cài đặt IIS cho FPT eInvoice API
# Chạy với quyền Administrator trên SERVER

param(
    [string]$SiteName = "FPTInvoiceAPI",
    [string]$Port = "5959",
    [string]$AppPath = "C:\inetpub\wwwroot\FPTInvoiceAPI",
    [string]$PythonPath = ""  # Sẽ tự động detect từ venv
)

# Tự động detect Python path từ venv
if ([string]::IsNullOrEmpty($PythonPath)) {
    $VenvPythonPath = Join-Path $AppPath "venv\Scripts\python.exe"
    if (Test-Path $VenvPythonPath) {
        $PythonPath = $VenvPythonPath
        Write-Host "✅ Detected Python in venv: $PythonPath" -ForegroundColor Green
    } else {
        Write-Error "❌ Python not found in venv: $VenvPythonPath"
        Write-Host "Please ensure the deployment package includes venv directory" -ForegroundColor Red
        exit 1
    }
}

Write-Host "🚀 Cài đặt IIS cho FPT eInvoice API" -ForegroundColor Green
Write-Host "📁 App Path: $AppPath" -ForegroundColor Yellow
Write-Host "🐍 Python Path: $PythonPath" -ForegroundColor Yellow
Write-Host "🌐 Site Name: $SiteName" -ForegroundColor Yellow
Write-Host "🔌 Port: $Port" -ForegroundColor Yellow

# 1. Kiểm tra quyền Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "❌ Script này cần chạy với quyền Administrator!"
    Write-Host "Nhấn chuột phải PowerShell và chọn 'Run as Administrator'" -ForegroundColor Red
    exit 1
}

# 2. Cài đặt IIS Features
Write-Host "📦 Cài đặt IIS Features..." -ForegroundColor Cyan

$features = @(
    "IIS-WebServerRole",
    "IIS-WebServer",
    "IIS-CommonHttpFeatures",
    "IIS-HttpErrors",
    "IIS-HttpLogging",
    "IIS-RequestFiltering",
    "IIS-StaticContent",
    "IIS-DefaultDocument",
    "IIS-DirectoryBrowsing",
    "IIS-ASPNET45",
    "IIS-NetFxExtensibility45",
    "IIS-ISAPIExtensions",
    "IIS-ISAPIFilter",
    "IIS-CGI",
    "IIS-ServerSideIncludes",
    "IIS-CustomLogging",
    "IIS-BasicAuthentication",
    "IIS-WindowsAuthentication",
    "IIS-IISCertificateProvider",
    "IIS-ManagementConsole"
)

foreach ($feature in $features) {
    Write-Host "  Installing $feature..." -ForegroundColor Gray
    Enable-WindowsOptionalFeature -Online -FeatureName $feature -All -NoRestart
}

# 3. Import WebAdministration module
Import-Module WebAdministration -ErrorAction SilentlyContinue

# 4. Tạo Application Pool
Write-Host "🏊 Tạo Application Pool..." -ForegroundColor Cyan
$poolName = "${SiteName}Pool"

if (Get-IISAppPool -Name $poolName -ErrorAction SilentlyContinue) {
    Write-Host "  Removing existing pool: $poolName" -ForegroundColor Yellow
    Remove-WebAppPool -Name $poolName
}

New-WebAppPool -Name $poolName
Set-ItemProperty -Path "IIS:\AppPools\$poolName" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
Set-ItemProperty -Path "IIS:\AppPools\$poolName" -Name "enable32BitAppOnWin64" -Value $false
Set-ItemProperty -Path "IIS:\AppPools\$poolName" -Name "managedRuntimeVersion" -Value ""
Set-ItemProperty -Path "IIS:\AppPools\$poolName" -Name "recycling.periodicRestart.time" -Value "00:00:00"

Write-Host "  ✅ Application Pool created: $poolName" -ForegroundColor Green

# 5. Tạo Website
Write-Host "🌐 Tạo Website..." -ForegroundColor Cyan

if (Get-Website -Name $SiteName -ErrorAction SilentlyContinue) {
    Write-Host "  Removing existing site: $SiteName" -ForegroundColor Yellow
    Remove-Website -Name $SiteName
}

New-Website -Name $SiteName -Port $Port -PhysicalPath $AppPath -ApplicationPool $poolName

Write-Host "  ✅ Website created: $SiteName" -ForegroundColor Green

# 6. Cấu hình FastCGI
Write-Host "⚡ Cấu hình FastCGI..." -ForegroundColor Cyan

# Kiểm tra wfastcgi trong venv
$VenvScriptsPath = Split-Path $PythonPath -Parent
$wfastcgiPath = Join-Path (Split-Path $VenvScriptsPath -Parent) "Lib\site-packages\wfastcgi.py"

Write-Host "  Checking wfastcgi at: $wfastcgiPath" -ForegroundColor Gray

if (-not (Test-Path $wfastcgiPath)) {
    Write-Host "  Installing wfastcgi in venv..." -ForegroundColor Yellow
    & $PythonPath -m pip install wfastcgi

    # Verify installation
    if (-not (Test-Path $wfastcgiPath)) {
        Write-Error "❌ Failed to install wfastcgi"
        exit 1
    }
}

# Enable wfastcgi
Write-Host "  Enabling wfastcgi..." -ForegroundColor Gray
& $PythonPath -m wfastcgi.enable

Write-Host "  ✅ FastCGI configured with venv Python" -ForegroundColor Green

# 7. Tạo thư mục logs
Write-Host "📝 Tạo thư mục logs..." -ForegroundColor Cyan
$logsPath = Join-Path $AppPath "logs"
if (-not (Test-Path $logsPath)) {
    New-Item -ItemType Directory -Path $logsPath -Force
}

Write-Host "  ✅ Logs directory created: $logsPath" -ForegroundColor Green

# 8. Cấu hình permissions
Write-Host "🔐 Cấu hình permissions..." -ForegroundColor Cyan

$iisUser = "IIS_IUSRS"
$appPoolUser = "IIS AppPool\$poolName"

# Cấp quyền cho IIS_IUSRS
icacls $AppPath /grant "${iisUser}:(OI)(CI)F" /T
icacls $logsPath /grant "${iisUser}:(OI)(CI)F" /T

# Cấp quyền cho Application Pool Identity
icacls $AppPath /grant "${appPoolUser}:(OI)(CI)F" /T
icacls $logsPath /grant "${appPoolUser}:(OI)(CI)F" /T

Write-Host "  ✅ Permissions configured" -ForegroundColor Green

# 9. Restart IIS
Write-Host "🔄 Restart IIS..." -ForegroundColor Cyan
iisreset

Write-Host "✅ Cài đặt hoàn tất!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Thông tin Website:" -ForegroundColor Cyan
Write-Host "  🌐 URL: http://localhost:$Port" -ForegroundColor White
Write-Host "  📁 Path: $AppPath" -ForegroundColor White
Write-Host "  🏊 App Pool: $poolName" -ForegroundColor White
Write-Host "  📝 Logs: $logsPath" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Các bước tiếp theo:" -ForegroundColor Yellow
Write-Host "  1. Cài đặt dependencies: pip install -r requirements.txt" -ForegroundColor White
Write-Host "  2. Tạo file .env với API_AUTH_KEY" -ForegroundColor White
Write-Host "  3. Test website: http://localhost:$Port" -ForegroundColor White
Write-Host "  4. Kiểm tra logs tại: $logsPath" -ForegroundColor White
