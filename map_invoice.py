#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Invoice Data Mapping Utility
Chuyển đổi dữ liệu hóa đơn từ format đầu vào sang format FPT eInvoice API
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class InvoiceMapper:
    """Class để chuyển đổi dữ liệu hóa đơn"""
    
    def __init__(self):
        self.required_fields = [
            'user.username', 'user.password',
            'inv.bname', 'inv.type', 'inv.serial',
            'inv.items'
        ]
    
    def load_input_invoice(self, file_path: str) -> Dict[str, Any]:
        """
        Đọc và parse file input_invoice.json
        
        Args:
            file_path: Đ<PERSON>ờng dẫn đến file input
            
        Returns:
            Dict chứa dữ liệu hóa đơn đầu vào
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File không tồn tại: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"✅ Đã load thành công file: {file_path}")
            return data
            
        except json.JSONDecodeError as e:
            logger.error(f"❌ Lỗi parse JSON: {e}")
            raise
        except Exception as e:
            logger.error(f"❌ Lỗi đọc file: {e}")
            raise
    
    def validate_input_data(self, data: Dict[str, Any]) -> bool:
        """
        Validate dữ liệu đầu vào
        
        Args:
            data: Dữ liệu hóa đơn đầu vào
            
        Returns:
            True nếu valid, raise Exception nếu invalid
        """
        def get_nested_value(obj, path):
            """Get value từ nested dict bằng dot notation"""
            keys = path.split('.')
            current = obj
            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return None
            return current
        
        missing_fields = []
        for field in self.required_fields:
            value = get_nested_value(data, field)
            if value is None or value == "":
                missing_fields.append(field)
        
        if missing_fields:
            raise ValueError(f"Thiếu các trường bắt buộc: {missing_fields}")
        
        # Validate items
        if not isinstance(data.get('inv', {}).get('items'), list):
            raise ValueError("inv.items phải là một array")
        
        if len(data['inv']['items']) == 0:
            raise ValueError("Hóa đơn phải có ít nhất 1 item")
        
        logger.info("✅ Validation thành công")
        return True
    
    def safe_float(self, value, default=0.0) -> float:
        """
        Safely convert value to float

        Args:
            value: Value to convert
            default: Default value if conversion fails

        Returns:
            Float value
        """
        if value is None or value == "":
            return default
        try:
            return float(value)
        except (ValueError, TypeError):
            return default

    def calculate_totals(self, items: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Tính toán các tổng tiền từ danh sách items

        Args:
            items: Danh sách items

        Returns:
            Dict chứa các tổng tiền đã tính
        """
        total_amount = 0
        total_vat = 0

        for item in items:
            amount = self.safe_float(item.get('amount', 0))
            vat_rate = self.safe_float(item.get('vrt', 0))

            total_amount += amount
            # Tính VAT: amount * vat_rate / 100
            item_vat = amount * vat_rate / 100
            total_vat += item_vat

        total_with_vat = total_amount + total_vat

        return {
            'sumv': total_amount,
            'sum': total_amount,
            'vatv': total_vat,
            'vat': total_vat,
            'totalv': total_with_vat,
            'total': total_with_vat
        }
    
    def convert_number_to_words(self, amount: float) -> str:
        """
        Chuyển đổi số tiền thành chữ (đơn giản)
        
        Args:
            amount: Số tiền
            
        Returns:
            Số tiền bằng chữ
        """
        # Đây là implementation đơn giản, có thể cải thiện
        if amount == 0:
            return "Không đồng"
        
        # Sử dụng format đơn giản
        amount_str = f"{int(amount):,}".replace(',', '.')
        return f"{amount_str} đồng"
    
    def map_invoice_data(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Chuyển đổi dữ liệu từ format đầu vào sang format FPT eInvoice API
        
        Args:
            input_data: Dữ liệu hóa đơn đầu vào
            
        Returns:
            Dict theo format FPT eInvoice API
        """
        inv_data = input_data.get('inv', {})
        
        # Tính toán totals từ items
        items = inv_data.get('items', [])
        totals = self.calculate_totals(items)
        
        # Sử dụng totals từ input nếu có, nếu không thì dùng calculated
        sumv = inv_data.get('sumv', totals['sumv'])
        vatv = inv_data.get('vatv', totals['vatv'])
        totalv = inv_data.get('totalv', totals['totalv'])
        
        # Tạo mapped data theo format FPT eInvoice API
        mapped_data = {
            # Basic invoice info
            "class": "0",
            "bname": inv_data.get('bname', ''),
            "idt": inv_data.get('idt') or datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "type": inv_data.get('type', '01GTKT'),
            "btax": inv_data.get('btax', ''),
            "buyer": inv_data.get('buyer', ''),
            "btel": inv_data.get('btel', ''),
            "bmail": inv_data.get('bmail', ''),
            "form": str(inv_data.get('form', '1')),
            "baddr": inv_data.get('baddr', ''),
            "bacc": inv_data.get('bacc', ''),
            "serial": inv_data.get('serial', ''),
            "paym": inv_data.get('paym', 'TM'),
            "curr": inv_data.get('curr', 'VND'),
            "exrt": self.safe_float(inv_data.get('exrt', 1)),
            "bcode": inv_data.get('bcode', ''),
            "bbank": inv_data.get('bbank', ''),
            "seq": inv_data.get('seq', ''),
            
            # Adjustment info
            "adj": {
                "ref": "",
                "rea": "",
                "des": ""
            },
            
            # Seller info
            "saddr": inv_data.get('saddr', ''),
            "sname": inv_data.get('sname', ''),
            "stax": inv_data.get('stax', ''),
            
            # Note and discount
            "note": inv_data.get('note', ''),
            "discount": self.safe_float(inv_data.get('discount', 0)),
            "discountv": self.safe_float(inv_data.get('discountv', 0)),

            # Totals
            "sumv": self.safe_float(sumv),
            "sum": self.safe_float(sumv),
            "vatv": self.safe_float(vatv),
            "vat": self.safe_float(vatv),
            "totalv": self.safe_float(totalv),
            "total": self.safe_float(totalv),

            # Special taxes
            "sctv": inv_data.get('sctv', ''),
            "sct": inv_data.get('sct', ''),
            "scv": inv_data.get('scv', ''),
            "sc": inv_data.get('sc', ''),
            "tradeamount": self.safe_float(inv_data.get('tradeamount', 0)),
            "tradeamountv": self.safe_float(inv_data.get('tradeamountv', 0)),
            
            # Amount in words
            "word": inv_data.get('word') or self.convert_number_to_words(totalv),
            
            # Additional fields
            "c1": inv_data.get('c1', ''),
            "budget_relationid": "",
            "idnumber": "",
            "passport_number": "",
            "listnum": "",
            "listdt": "",
            
            # Items
            "items": self.map_items(items),
            "fees": [],
            "items_special_types": {},
            
            # Invoice metadata
            "name": "HÓA ĐƠN GIÁ TRỊ GIA TĂNG",
            "tax": self.calculate_tax_summary(items),
            "type_ref": int(inv_data.get('type_ref', 1)),
            "hascode": 0,
            "ma_cqthu": "",
            "sendtype": int(inv_data.get('sendtype', 1)),
            "invtype": "",
            "sign": 1,
            "status": 1,
            "sec": ""
        }
        
        return mapped_data

    def map_items(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Chuyển đổi danh sách items sang format FPT eInvoice API

        Args:
            items: Danh sách items từ input

        Returns:
            Danh sách items theo format FPT eInvoice API
        """
        mapped_items = []

        for i, item in enumerate(items):
            quantity = self.safe_float(item.get('quantity', 0))
            price = self.safe_float(item.get('price', 0))
            amount = self.safe_float(item.get('amount', quantity * price))
            vat_rate = self.safe_float(item.get('vrt', 0))
            vat_amount = amount * vat_rate / 100
            total_with_vat = amount + vat_amount

            mapped_item = {
                "quantity": quantity,
                "price": price,
                "unit": item.get('unit', ''),
                "vat": vat_amount,
                "amount": amount,
                "total": total_with_vat,
                "vrt": str(int(vat_rate)),
                "vrn": f"{int(vat_rate)}%",
                "id": item.get('id', int(datetime.now().timestamp() * 1000) + i),
                "line":  i + 1,
                "code": item.get('code', ''),
                "name": item.get('name', ''),
                "type": item.get('type', ''),
                "c0": item.get('c0', '')
            }

            mapped_items.append(mapped_item)

        return mapped_items

    def calculate_tax_summary(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Tính toán tóm tắt thuế theo từng mức thuế suất

        Args:
            items: Danh sách items

        Returns:
            Danh sách tóm tắt thuế
        """
        tax_summary = {}

        for item in items:
            vat_rate_float = self.safe_float(item.get('vrt', 0))
            vat_rate = str(int(vat_rate_float))
            amount = self.safe_float(item.get('amount', 0))
            vat_amount = amount * vat_rate_float / 100

            if vat_rate not in tax_summary:
                tax_summary[vat_rate] = {
                    "vrt": vat_rate,
                    "vrn": f"{vat_rate}%",
                    "amt": 0,
                    "amtv": 0,
                    "vat": 0,
                    "vatv": 0
                }

            tax_summary[vat_rate]["amt"] += amount
            tax_summary[vat_rate]["amtv"] += amount
            tax_summary[vat_rate]["vat"] += vat_amount
            tax_summary[vat_rate]["vatv"] += vat_amount

        return list(tax_summary.values())


def load_and_convert_invoice(input_file: str, output_file: Optional[str] = None) -> Dict[str, Any]:
    """
    Function chính để load và convert hóa đơn

    Args:
        input_file: Đường dẫn file input
        output_file: Đường dẫn file output (optional)

    Returns:
        Dict chứa dữ liệu đã convert
    """
    mapper = InvoiceMapper()

    try:
        # Load input data
        logger.info(f"🔄 Đang load file: {input_file}")
        input_data = mapper.load_input_invoice(input_file)

        # Validate input data
        logger.info("🔍 Đang validate dữ liệu...")
        mapper.validate_input_data(input_data)

        # Convert data
        logger.info("🔄 Đang chuyển đổi dữ liệu...")
        converted_data = mapper.map_invoice_data(input_data)

        # Save to output file if specified
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(converted_data, f, ensure_ascii=False, indent=2)
            logger.info(f"✅ Đã lưu kết quả vào: {output_file}")

        logger.info("✅ Chuyển đổi hoàn tất!")
        return converted_data

    except Exception as e:
        logger.error(f"❌ Lỗi trong quá trình chuyển đổi: {e}")
        raise


def test_with_api_server(converted_data: Dict[str, Any], api_url: str = "http://localhost:5959", api_key: str = "") -> bool:
    """
    Test converted data với API server

    Args:
        converted_data: Dữ liệu đã convert
        api_url: URL của API server
        api_key: API key để authentication

    Returns:
        True nếu test thành công
    """
    try:
        import requests

        # Test endpoint
        endpoint = f"{api_url}/create-view-sample-invoice"

        headers = {
            "Content-Type": "application/json"
        }

        if api_key:
            headers["Authorization"] = f"Bearer {api_key}"

        logger.info(f"🧪 Testing với API server: {endpoint}")

        response = requests.post(endpoint, json=converted_data, headers=headers, timeout=30)

        if response.status_code == 200:
            logger.info("✅ API test thành công - PDF được tạo")
            return True
        else:
            logger.error(f"❌ API test thất bại: {response.status_code} - {response.text}")
            return False

    except ImportError:
        logger.warning("⚠️ Không có requests module, bỏ qua API test")
        return True
    except Exception as e:
        logger.error(f"❌ Lỗi khi test API: {e}")
        return False


def main():
    """Function main để test"""
    import argparse

    parser = argparse.ArgumentParser(description='FPT eInvoice Data Mapper')
    parser.add_argument('--input', '-i', default='sample/input_invoice.json', help='Input file path')
    parser.add_argument('--output', '-o', default='sample/converted_invoice.json', help='Output file path')
    parser.add_argument('--test-api', action='store_true', help='Test với API server')
    parser.add_argument('--api-url', default='http://localhost:5959', help='API server URL')
    parser.add_argument('--api-key', default='', help='API key cho authentication')

    args = parser.parse_args()

    try:
        print("🚀 FPT eInvoice Data Mapper")
        print("=" * 50)

        # Convert invoice
        result = load_and_convert_invoice(args.input, args.output)

        # Display summary
        print(f"📊 Thông tin hóa đơn đã convert:")
        print(f"   - Khách hàng: {result.get('bname', 'N/A')}")
        print(f"   - Loại hóa đơn: {result.get('type', 'N/A')}")
        print(f"   - Serial: {result.get('serial', 'N/A')}")
        print(f"   - Số items: {len(result.get('items', []))}")
        print(f"   - Tổng tiền (chưa VAT): {result.get('sum', 0):,.0f} {result.get('curr', 'VND')}")
        print(f"   - VAT: {result.get('vat', 0):,.0f} {result.get('curr', 'VND')}")
        print(f"   - Tổng tiền (có VAT): {result.get('total', 0):,.0f} {result.get('curr', 'VND')}")
        print(f"   - Bằng chữ: {result.get('word', 'N/A')}")

        print(f"\n✅ File output: {args.output}")

        # Test với API server nếu được yêu cầu
        if args.test_api:
            print(f"\n🧪 Testing với API server...")
            success = test_with_api_server(result, args.api_url, args.api_key)
            if success:
                print("✅ API test thành công!")
            else:
                print("❌ API test thất bại!")
                return 1

    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
