@echo off
REM Script tạo virtual environment cho FPT eInvoice API
REM Chạy trên máy local trước khi deploy lên server

echo ========================================
echo  SETUP VIRTUAL ENVIRONMENT - LOCAL
echo ========================================

REM Kiểm tra Python version
echo 🐍 Checking Python version...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python not found! Please install Python 3.8+
    pause
    exit /b 1
)

REM Xóa venv cũ nếu có
if exist "venv" (
    echo 🗑️ Removing existing venv...
    rmdir /s /q venv
)

REM Tạo virtual environment mới
echo 📦 Creating virtual environment...
python -m venv venv
if %errorlevel% neq 0 (
    echo ❌ Failed to create virtual environment
    pause
    exit /b 1
)

REM Kích hoạt venv
echo ⚡ Activating virtual environment...
call venv\Scripts\activate.bat

REM Upgrade pip
echo 📈 Upgrading pip...
python -m pip install --upgrade pip

REM Cài đặt dependencies
echo 📦 Installing dependencies from requirements.txt...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

REM Cài đặt wfastcgi cho IIS
echo 🚀 Installing wfastcgi for IIS...
pip install wfastcgi
if %errorlevel% neq 0 (
    echo ❌ Failed to install wfastcgi
    pause
    exit /b 1
)

REM Hiển thị thông tin packages đã cài
echo ✅ Installed packages:
pip list

echo.
echo ========================================
echo  SETUP COMPLETED SUCCESSFULLY!
echo ========================================
echo 📁 Virtual environment: %CD%\venv
echo 🐍 Python executable: %CD%\venv\Scripts\python.exe
echo 📦 Packages installed: %CD%\venv\Lib\site-packages
echo.
echo Next steps:
echo 1. Test the application locally
echo 2. Copy project to server
echo 3. Configure IIS
echo.
pause
