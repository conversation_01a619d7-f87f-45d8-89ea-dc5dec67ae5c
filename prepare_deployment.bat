@echo off
REM Script chuẩn bị files để deploy lên server

echo ========================================
echo  PREPARING DEPLOYMENT PACKAGE
echo ========================================

set DEPLOY_DIR=deployment_package

REM Xóa thư mục deployment cũ
if exist "%DEPLOY_DIR%" (
    echo 🗑️ Removing old deployment package...
    rmdir /s /q "%DEPLOY_DIR%"
)

REM Tạo thư mục deployment
echo 📁 Creating deployment directory...
mkdir "%DEPLOY_DIR%"

echo 📦 Copying application files...

REM Copy Python application files
copy "*.py" "%DEPLOY_DIR%\"
copy "requirements.txt" "%DEPLOY_DIR%\"
copy "web.config" "%DEPLOY_DIR%\"

REM Copy .env template (create a sample .env file)
(
echo # FPT eInvoice API Configuration
echo # Copy this file to .env and update with your values
echo.
echo # API Authentication
echo API_AUTH_KEY=your_production_api_key_here
echo.
echo # FPT eInvoice API Configuration
echo FPT_API_BASE_URL=https://api-uat.einvoice.fpt.com.vn
echo FPT_SIGNIN_URL=https://api-uat.einvoice.fpt.com.vn/c_signin
echo.
echo # Selenium Configuration
echo HEADLESS_MODE=true
echo SELENIUM_TIMEOUT=30
echo WEBDRIVER_TIMEOUT=60
echo.
echo # Flask Configuration
echo FLASK_ENV=production
echo FLASK_DEBUG=false
echo.
echo # Logging Configuration
echo LOG_LEVEL=INFO
echo LOG_FILE=logs/app.log
echo.
echo # Security Settings
echo SECRET_KEY=your_secret_key_here
echo.
echo # Performance Settings
echo MAX_CONTENT_LENGTH=52428800
echo REQUEST_TIMEOUT=90
echo.
echo # File Storage
echo INVOICES_DIR=./invoices
echo FILES_DIR=./files
) > "%DEPLOY_DIR%\.env.template"

REM Copy sample data
if exist "sample" (
    echo 📄 Copying sample directory...
    xcopy "sample" "%DEPLOY_DIR%\sample\" /E /I
)

REM Copy virtual environment
echo 🐍 Copying virtual environment...
xcopy "venv" "%DEPLOY_DIR%\venv\" /E /I /Q
if %errorlevel% neq 0 (
    echo ❌ Failed to copy virtual environment
    pause
    exit /b 1
)

REM Tạo thư mục cần thiết
echo 📁 Creating required directories...
mkdir "%DEPLOY_DIR%\logs"
mkdir "%DEPLOY_DIR%\invoices"
mkdir "%DEPLOY_DIR%\files"

REM Tạo script cài đặt cho server
(
echo @echo off
echo REM Script cài đặt trên server
echo echo Installing FPT eInvoice API on server...
echo.
echo REM Copy .env template
echo if not exist ".env" ^(
echo     copy ".env.template" ".env"
echo     echo ✅ Created .env file - PLEASE EDIT WITH YOUR SETTINGS
echo ^)
echo.
echo REM Enable wfastcgi
echo echo Enabling wfastcgi...
echo venv\Scripts\python.exe -m wfastcgi.enable
echo.
echo echo ========================================
echo echo  INSTALLATION COMPLETED
echo echo ========================================
echo echo Next steps:
echo echo 1. Edit .env file with your API_AUTH_KEY
echo echo 2. Configure IIS site to point to this directory
echo echo 3. Test: http://localhost:5959/health
echo echo.
echo pause
) > "%DEPLOY_DIR%\install_on_server.bat"

echo.
echo ========================================
echo  DEPLOYMENT PACKAGE READY
echo ========================================
echo 📁 Package location: %CD%\%DEPLOY_DIR%
echo.
echo 📋 Package contents:
dir "%DEPLOY_DIR%" /b
echo.
echo 🚀 Next steps:
echo 1. Copy %DEPLOY_DIR% to server
echo 2. Run install_on_server.bat on server
echo 3. Configure IIS site
echo.
pause
