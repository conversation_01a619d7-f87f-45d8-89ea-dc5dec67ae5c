@echo off
REM Script chuẩn bị files để deploy lên server
REM Tạo package deployment với tất cả files cần thiết

echo ========================================
echo  PREPARING DEPLOYMENT PACKAGE
echo ========================================

set DEPLOY_DIR=deployment_package
set SERVER_PATH=C:\inetpub\wwwroot\FPTInvoiceAPI

REM Xóa thư mục deployment cũ
if exist "%DEPLOY_DIR%" (
    echo 🗑️ Removing old deployment package...
    rmdir /s /q "%DEPLOY_DIR%"
)

REM Tạo thư mục deployment
echo 📁 Creating deployment directory...
mkdir "%DEPLOY_DIR%"

echo 📦 Copying application files...

REM Copy Python application files
copy "*.py" "%DEPLOY_DIR%\"
copy "requirements.txt" "%DEPLOY_DIR%\"
copy "web.config" "%DEPLOY_DIR%\"
copy "DEPLOYMENT_GUIDE.md" "%DEPLOY_DIR%\"

REM Copy .env template (NOT the actual .env with secrets)
copy ".env.production" "%DEPLOY_DIR%\"

REM Copy sample data
if exist "sample" (
    echo 📄 Copying sample directory...
    xcopy "sample" "%DEPLOY_DIR%\sample\" /E /I
)

REM Copy virtual environment
echo 🐍 Copying virtual environment...
xcopy "venv" "%DEPLOY_DIR%\venv\" /E /I /Q
if %errorlevel% neq 0 (
    echo ❌ Failed to copy virtual environment
    pause
    exit /b 1
)

REM Tạo thư mục cần thiết
echo 📁 Creating required directories...
mkdir "%DEPLOY_DIR%\logs"
mkdir "%DEPLOY_DIR%\invoices"
mkdir "%DEPLOY_DIR%\files"

REM Tạo file cấu hình server-specific
echo 📝 Creating server configuration files...

REM Tạo web.config cho server
(
echo ^<?xml version="1.0" encoding="utf-8"?^>
echo ^<configuration^>
echo   ^<system.webServer^>
echo     ^<handlers^>
echo       ^<add name="PythonHandler" 
echo            path="*" 
echo            verb="*" 
echo            modules="FastCgiModule"
echo            scriptProcessor="%SERVER_PATH%\venv\Scripts\python.exe|%SERVER_PATH%\venv\Lib\site-packages\wfastcgi.py"
echo            resourceType="Unspecified" 
echo            requireAccess="Script" /^>
echo     ^</handlers^>
echo     ^<fastCgi^>
echo       ^<application fullPath="%SERVER_PATH%\venv\Scripts\python.exe"
echo                    arguments="%SERVER_PATH%\venv\Lib\site-packages\wfastcgi.py"
echo                    maxInstances="4"
echo                    idleTimeout="1800"
echo                    activityTimeout="30"
echo                    requestTimeout="90"
echo                    instanceMaxRequests="10000"^>
echo         ^<environmentVariables^>
echo           ^<add name="WSGI_HANDLER" value="wsgi.application" /^>
echo           ^<add name="PYTHONPATH" value="%SERVER_PATH%" /^>
echo           ^<add name="WSGI_LOG" value="%SERVER_PATH%\logs\wsgi.log" /^>
echo           ^<add name="FLASK_ENV" value="production" /^>
echo           ^<add name="PYTHONIOENCODING" value="utf-8" /^>
echo         ^</environmentVariables^>
echo       ^</application^>
echo     ^</fastCgi^>
echo   ^</system.webServer^>
echo ^</configuration^>
) > "%DEPLOY_DIR%\web.config.server"

REM Tạo script cài đặt cho server
(
echo @echo off
echo REM Script cài đặt trên server
echo echo Installing FPT eInvoice API on server...
echo.
echo REM Copy .env template
echo if not exist ".env" ^(
echo     copy ".env.production" ".env"
echo     echo ✅ Created .env file - PLEASE EDIT WITH YOUR SETTINGS
echo ^)
echo.
echo REM Enable wfastcgi
echo echo Enabling wfastcgi...
echo venv\Scripts\python.exe -m wfastcgi.enable
echo.
echo REM Set permissions
echo echo Setting permissions...
echo icacls . /grant "IIS_IUSRS:^(OI^)^(CI^)F" /T
echo icacls . /grant "IIS AppPool\DefaultAppPool:^(OI^)^(CI^)F" /T
echo.
echo echo ========================================
echo echo  INSTALLATION COMPLETED
echo echo ========================================
echo echo Next steps:
echo echo 1. Edit .env file with your API_AUTH_KEY
echo echo 2. Configure IIS site to point to this directory
echo echo 3. Test the application
echo echo.
echo pause
) > "%DEPLOY_DIR%\install_on_server.bat"

REM Tạo file README cho deployment
(
echo # FPT eInvoice API - Deployment Package
echo.
echo ## Server Requirements
echo - Windows Server with IIS
echo - FastCGI module enabled
echo - Python 3.8+ ^(included in venv^)
echo.
echo ## Installation Steps
echo.
echo 1. Copy this entire directory to: C:\inetpub\wwwroot\FPTInvoiceAPI
echo 2. Run: install_on_server.bat
echo 3. Edit .env file with your settings
echo 4. Configure IIS site
echo 5. Test the application
echo.
echo ## Important Files
echo - wsgi.py: WSGI entry point
echo - web.config.server: IIS configuration template
echo - venv/: Complete Python environment
echo - .env.production: Environment template
echo.
echo ## Support
echo See DEPLOYMENT_GUIDE.md for detailed instructions
) > "%DEPLOY_DIR%\README_DEPLOYMENT.txt"

echo.
echo ========================================
echo  DEPLOYMENT PACKAGE READY
echo ========================================
echo 📁 Package location: %CD%\%DEPLOY_DIR%
echo 📦 Package size:
dir "%DEPLOY_DIR%" /s /-c | find "File(s)"
echo.
echo 📋 Package contents:
dir "%DEPLOY_DIR%" /b
echo.
echo 🚀 Ready to copy to server: %SERVER_PATH%
echo.
echo Next steps:
echo 1. Copy %DEPLOY_DIR% to server
echo 2. Run install_on_server.bat on server
echo 3. Configure IIS
echo.
pause
